<script lang="ts">
import { ref } from "vue";

export default {
  name: "Navbar",
  setup() {
    const isProductsOpen = ref(false);
    const isResourcesOpen = ref(false);

    const toggleProducts = () => {
      isProductsOpen.value = !isProductsOpen.value;
      isResourcesOpen.value = false;
    };

    const toggleResources = () => {
      isResourcesOpen.value = !isResourcesOpen.value;
      isProductsOpen.value = false;
    };

    const closeDropdowns = () => {
      isProductsOpen.value = false;
      isResourcesOpen.value = false;
    };

    return {
      isProductsOpen,
      isResourcesOpen,
      toggleProducts,
      toggleResources,
      closeDropdowns,
    };
  },
};
</script>

<template>
  <nav class="navbar" @click="closeDropdowns">
    <!-- Logo -->
    <div class="navbar-brand">
      <a href="/" class="logo">
        <img src="/logo.svg" alt="Logo" class="logo-img" />
        <span class="logo-text">Brand</span>
      </a>
    </div>

    <!-- Navigation Links -->
    <div class="navbar-nav">
      <!-- Regular Links -->
      <a href="/about" class="nav-link">About</a>
      <a href="/contact" class="nav-link">Contact</a>
      <a href="/blog" class="nav-link">Blog</a>

      <!-- Products Dropdown -->
      <div class="dropdown" @click.stop>
        <button
          @click="toggleProducts"
          class="nav-link dropdown-toggle"
          :class="{ active: isProductsOpen }"
        >
          Products
          <svg
            class="dropdown-icon"
            :class="{ rotated: isProductsOpen }"
            viewBox="0 0 20 20"
            fill="currentColor"
          >
            <path
              fill-rule="evenodd"
              d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z"
              clip-rule="evenodd"
            />
          </svg>
        </button>
        <div v-show="isProductsOpen" class="dropdown-menu">
          <a href="/products/web" class="dropdown-item">Web Development</a>
          <a href="/products/mobile" class="dropdown-item">Mobile Apps</a>
          <a href="/products/design" class="dropdown-item">UI/UX Design</a>
        </div>
      </div>

      <!-- Resources Dropdown -->
      <div class="dropdown" @click.stop>
        <button
          @click="toggleResources"
          class="nav-link dropdown-toggle"
          :class="{ active: isResourcesOpen }"
        >
          Resources
          <svg
            class="dropdown-icon"
            :class="{ rotated: isResourcesOpen }"
            viewBox="0 0 20 20"
            fill="currentColor"
          >
            <path
              fill-rule="evenodd"
              d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z"
              clip-rule="evenodd"
            />
          </svg>
        </button>
        <div v-show="isResourcesOpen" class="dropdown-menu">
          <a href="/docs" class="dropdown-item">Documentation</a>
          <a href="/tutorials" class="dropdown-item">Tutorials</a>
          <a href="/support" class="dropdown-item">Support</a>
        </div>
      </div>
    </div>

    <!-- CTA Button -->
    <div class="navbar-cta">
      <button class="cta-button">Get Started</button>
    </div>
  </nav>
</template>

<style lang="css" scoped>
.navbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 2rem;
  height: 70px;
  background-color: #fff;
  border-bottom: 1px solid #e5e7eb;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
  position: relative;
  z-index: 50;
}

/* Logo */
.navbar-brand {
  flex-shrink: 0;
}

.logo {
  display: flex;
  align-items: center;
  text-decoration: none;
  color: #1f2937;
  font-weight: 600;
  font-size: 1.25rem;
}

.logo-img {
  height: 32px;
  width: 32px;
  margin-right: 0.5rem;
}

.logo-text {
  color: #1f2937;
}

/* Navigation */
.navbar-nav {
  display: flex;
  align-items: center;
  gap: 2rem;
}

.nav-link {
  display: flex;
  align-items: center;
  padding: 0.5rem 0;
  color: #6b7280;
  text-decoration: none;
  font-weight: 500;
  transition: color 0.2s ease;
  background: none;
  border: none;
  cursor: pointer;
  font-size: 1rem;
}

.nav-link:hover,
.nav-link.active {
  color: #3b82f6;
}

/* Dropdown */
.dropdown {
  position: relative;
}

.dropdown-toggle {
  gap: 0.25rem;
}

.dropdown-icon {
  width: 1rem;
  height: 1rem;
  transition: transform 0.2s ease;
}

.dropdown-icon.rotated {
  transform: rotate(180deg);
}

.dropdown-menu {
  position: absolute;
  top: 100%;
  left: 0;
  min-width: 12rem;
  background-color: #fff;
  border: 1px solid #e5e7eb;
  border-radius: 0.5rem;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
  padding: 0.5rem 0;
  margin-top: 0.5rem;
  z-index: 100;
}

.dropdown-item {
  display: block;
  padding: 0.75rem 1rem;
  color: #6b7280;
  text-decoration: none;
  transition: background-color 0.2s ease, color 0.2s ease;
}

.dropdown-item:hover {
  background-color: #f3f4f6;
  color: #3b82f6;
}

/* CTA Button */
.navbar-cta {
  flex-shrink: 0;
}

.cta-button {
  background-color: #3b82f6;
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 0.5rem;
  font-weight: 600;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.cta-button:hover {
  background-color: #2563eb;
}

/* Responsive */
@media (max-width: 768px) {
  .navbar {
    padding: 0 1rem;
  }

  .navbar-nav {
    gap: 1rem;
  }

  .nav-link {
    font-size: 0.875rem;
  }

  .cta-button {
    padding: 0.5rem 1rem;
    font-size: 0.875rem;
  }
}
</style>
